'use client';

import { useState, useEffect } from 'react';
import { User } from 'firebase/auth';
import { 
  onAuthStateChange, 
  getAdminUserProfile, 
  AdminUserProfile,
  checkSessionTimeout,
  updateLastActivity,
  signOutAdmin
} from '@/lib/auth';
import { securityConfig } from '@/lib/env';

export interface AdminAuthState {
  user: User | null;
  profile: AdminUserProfile | null;
  loading: boolean;
  error: string | null;
  sessionValid: boolean;
}

export function useAuth(): AdminAuthState {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<AdminUserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionValid, setSessionValid] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChange((user) => {
      try {
        setUser(user);
        setProfile(user ? getAdminUserProfile(user) : null);
        setError(null);
        
        // Check session validity
        if (user) {
          const isSessionValid = !checkSessionTimeout();
          setSessionValid(isSessionValid);
          
          if (!isSessionValid) {
            signOutAdmin();
            setError('Session expired. Please sign in again.');
          } else {
            updateLastActivity();
          }
        }
      } catch (err) {
        setError('Failed to get admin user profile');
        console.error('Admin auth state change error:', err);
      } finally {
        setLoading(false);
      }
    });

    return unsubscribe;
  }, []);

  // Set up session timeout monitoring
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(() => {
      if (checkSessionTimeout()) {
        setSessionValid(false);
        signOutAdmin();
        setError('Session expired due to inactivity.');
      } else {
        updateLastActivity();
      }
    }, 60000); // Check every minute

    // Update activity on user interaction
    const handleActivity = () => {
      updateLastActivity();
    };

    window.addEventListener('mousedown', handleActivity);
    window.addEventListener('keydown', handleActivity);
    window.addEventListener('scroll', handleActivity);

    return () => {
      clearInterval(interval);
      window.removeEventListener('mousedown', handleActivity);
      window.removeEventListener('keydown', handleActivity);
      window.removeEventListener('scroll', handleActivity);
    };
  }, [user]);

  return { user, profile, loading, error, sessionValid };
}

// Hook for checking if user is authenticated admin
export function useAuthState(): { 
  isAuthenticated: boolean; 
  isAdmin: boolean;
  loading: boolean;
  sessionValid: boolean;
} {
  const { user, profile, loading, sessionValid } = useAuth();
  return { 
    isAuthenticated: !!user && sessionValid, 
    isAdmin: !!profile?.isAdmin && sessionValid,
    loading,
    sessionValid
  };
}

// Hook for getting admin user profile
export function useAdminProfile(): { 
  profile: AdminUserProfile | null; 
  loading: boolean;
  hasPermission: (permission: string) => boolean;
} {
  const { profile, loading } = useAuth();
  
  const hasPermission = (permission: string): boolean => {
    return profile?.permissions.includes(permission) || false;
  };

  return { profile, loading, hasPermission };
}

// Hook for session management
export function useSession(): {
  sessionValid: boolean;
  timeRemaining: number;
  extendSession: () => void;
} {
  const { sessionValid } = useAuth();
  const [timeRemaining, setTimeRemaining] = useState(securityConfig.sessionTimeout);

  useEffect(() => {
    const interval = setInterval(() => {
      if (typeof window !== 'undefined') {
        const lastActivity = localStorage.getItem('last_activity');
        if (lastActivity) {
          const elapsed = (Date.now() - parseInt(lastActivity)) / 1000;
          const remaining = Math.max(0, securityConfig.sessionTimeout - elapsed);
          setTimeRemaining(remaining);
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const extendSession = () => {
    updateLastActivity();
    setTimeRemaining(securityConfig.sessionTimeout);
  };

  return { sessionValid, timeRemaining, extendSession };
}
