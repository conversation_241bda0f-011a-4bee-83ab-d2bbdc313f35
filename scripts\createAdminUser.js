/**
 * Direct Script to Create Admin Collections and Users
 * 
 * This script directly creates the admin collections and users in your Firebase project
 * using the Firebase Admin SDK with your actual credentials.
 */

const admin = require('firebase-admin');

// Your Firebase configuration from .env.local
const firebaseConfig = {
  projectId: 'encreasl-daa43',
  privateKey: `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
  clientEmail: '<EMAIL>'
};

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert(firebaseConfig),
  projectId: firebaseConfig.projectId
});

const db = admin.firestore();
const auth = admin.auth();

// ========================================
// CREATE ADMIN ROLES
// ========================================

async function createAdminRoles() {
  console.log('🔧 Creating admin roles...');
  
  const roles = [
    {
      id: 'super-admin',
      name: 'super-admin',
      displayName: 'Super Admin',
      description: 'Has full access to all system features and settings',
      permissions: ['*'],
      level: 10,
      isSystemRole: true,
      isCustomRole: false,
      userCount: 0,
      isActive: true
    },
    {
      id: 'admin',
      name: 'admin',
      displayName: 'Administrator',
      description: 'Can manage most system settings and users',
      permissions: [
        'users.view', 'users.create', 'users.edit',
        'content.view', 'content.create', 'content.edit', 'content.publish',
        'media.view', 'media.upload', 'media.delete',
        'settings.view'
      ],
      level: 8,
      parentRoleRef: 'admin_roles/super-admin',
      childRoleRefs: ['admin_roles/content-manager', 'admin_roles/editor'],
      isSystemRole: true,
      isCustomRole: false,
      userCount: 0,
      isActive: true
    },
    {
      id: 'content-manager',
      name: 'content-manager',
      displayName: 'Content Manager',
      description: 'Can manage all content and media',
      permissions: [
        'content.view', 'content.create', 'content.edit', 'content.publish',
        'media.view', 'media.upload', 'media.delete',
        'analytics.view'
      ],
      level: 6,
      parentRoleRef: 'admin_roles/admin',
      childRoleRefs: ['admin_roles/editor'],
      isSystemRole: true,
      isCustomRole: false,
      userCount: 0,
      isActive: true
    },
    {
      id: 'editor',
      name: 'editor',
      displayName: 'Editor',
      description: 'Can create and edit content but cannot publish',
      permissions: [
        'content.view', 'content.create', 'content.edit',
        'media.view', 'media.upload'
      ],
      level: 4,
      parentRoleRef: 'admin_roles/content-manager',
      childRoleRefs: [],
      isSystemRole: true,
      isCustomRole: false,
      userCount: 0,
      isActive: true
    }
  ];

  for (const role of roles) {
    await db.collection('admin_roles').doc(role.id).set({
      ...role,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system'
    });
    console.log(`✅ Created role: ${role.displayName}`);
  }
}

// ========================================
// CREATE ADMIN PERMISSIONS
// ========================================

async function createAdminPermissions() {
  console.log('🔧 Creating admin permissions...');
  
  const categories = {
    content: ['view', 'create', 'edit', 'delete', 'publish', 'schedule'],
    media: ['view', 'upload', 'edit', 'delete', 'organize'],
    users: ['view', 'create', 'edit', 'delete', 'change-roles'],
    settings: ['view', 'edit', 'manage-integrations', 'manage-backups'],
    analytics: ['view', 'export', 'configure-dashboards']
  };

  for (const [category, actions] of Object.entries(categories)) {
    for (const action of actions) {
      const permissionId = `${category}.${action}`;
      const permission = {
        id: permissionId,
        name: permissionId,
        displayName: `${capitalizeFirstLetter(action)} ${capitalizeFirstLetter(category)}`,
        description: `Can ${action} ${category}`,
        category,
        resource: category,
        action,
        isSystemPermission: true,
        isActive: true,
        roleCount: 0,
        userCount: 0,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      };
      
      await db.collection('admin_permissions').doc(permissionId).set(permission);
      console.log(`✅ Created permission: ${permission.displayName}`);
    }
  }
}

// ========================================
// CREATE SUPER ADMIN USER
// ========================================

async function createSuperAdminUser() {
  console.log('🔧 Creating super admin user...');
  
  const superAdminEmail = '<EMAIL>';
  const superAdminPassword = 'AdminPassword123!';
  const superAdminName = 'Super Administrator';
  
  try {
    // Create user in Firebase Auth
    let userRecord;
    try {
      userRecord = await auth.createUser({
        email: superAdminEmail,
        password: superAdminPassword,
        displayName: superAdminName,
        emailVerified: true
      });
      console.log(`✅ Created Firebase Auth user: ${superAdminEmail}`);
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        userRecord = await auth.getUserByEmail(superAdminEmail);
        console.log(`✅ Using existing Firebase Auth user: ${superAdminEmail}`);
      } else {
        throw error;
      }
    }

    // Set custom claims
    await auth.setCustomUserClaims(userRecord.uid, {
      admin: true,
      superAdmin: true,
      role: 'super-admin',
      permissions: ['*']
    });

    // Create admin user document in Firestore
    const adminUser = {
      id: userRecord.uid,
      email: superAdminEmail,
      displayName: superAdminName,
      authProvider: 'email',
      emailVerified: true,
      
      // Role information
      roleRef: 'admin_roles/super-admin',
      roleName: 'Super Admin',
      permissions: ['*'],
      isActive: true,
      isSuperAdmin: true,
      
      // Profile information
      firstName: 'Super',
      lastName: 'Administrator',
      title: 'Super Administrator',
      
      // CMS capabilities (all enabled for super admin)
      cmsPermissions: {
        content: {
          canCreate: true,
          canEdit: true,
          canDelete: true,
          canPublish: true,
          canSchedule: true,
        },
        media: {
          canUpload: true,
          canDelete: true,
          canOrganize: true,
          maxUploadSize: 100,
        },
        users: {
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true,
          canChangeRoles: true,
        },
        analytics: {
          canView: true,
          canExport: true,
          canConfigureDashboards: true,
        },
        settings: {
          canViewSystem: true,
          canEditSystem: true,
          canManageIntegrations: true,
          canManageBackups: true,
        },
      },
      
      // Workflow permissions
      workflowPermissions: {
        canApproveContent: true,
        canRejectContent: true,
        canAssignTasks: true,
        canCreateWorkflows: true,
        approvalLevel: 5,
      },
      
      // Access control
      accessRestrictions: {
        requireMFA: false,
        sessionTimeout: 60,
        maxConcurrentSessions: 3,
      },
      
      // Activity tracking
      loginCount: 0,
      failedLoginAttempts: 0,
      
      // Preferences
      preferences: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        dashboardLayout: 'grid',
        notificationsEnabled: true,
        emailNotifications: true,
      },
      
      // Metadata
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system',
      updatedBy: 'system',
      version: 1,
      
      // Soft delete
      isDeleted: false,
    };

    await db.collection('admin_users').doc(userRecord.uid).set(adminUser);
    console.log(`✅ Created admin user document: ${superAdminEmail}`);

    // Update role user count
    await db.collection('admin_roles').doc('super-admin').update({
      userCount: admin.firestore.FieldValue.increment(1),
      lastUsedAt: admin.firestore.Timestamp.now()
    });

    // Create admin preferences
    await db.collection('admin_preferences').doc(userRecord.uid).set({
      userId: userRecord.uid,
      theme: 'light',
      language: 'en',
      timezone: 'UTC',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      dashboardLayout: 'grid',
      notificationsEnabled: true,
      emailNotifications: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    // Create audit log
    await db.collection('admin_audit_logs').add({
      adminRef: `admin_users/system`,
      adminEmail: 'system',
      adminName: 'System',
      action: 'user.create',
      resource: 'admin_users',
      resourceId: userRecord.uid,
      description: `Created super admin user: ${superAdminEmail}`,
      ipAddress: '0.0.0.0',
      userAgent: 'Setup Script',
      timestamp: admin.firestore.Timestamp.now(),
      severity: 'high',
      category: 'authentication',
      success: true,
      searchKeywords: ['admin', 'create', 'super-admin', superAdminEmail],
      dateString: new Date().toISOString().split('T')[0]
    });

    console.log(`✅ Super admin user created successfully!`);
    console.log(`📧 Email: ${superAdminEmail}`);
    console.log(`🔑 Password: ${superAdminPassword}`);
    console.log(`🆔 UID: ${userRecord.uid}`);
    
    return userRecord.uid;

  } catch (error) {
    console.error('❌ Error creating super admin user:', error);
    throw error;
  }
}

// ========================================
// MAIN EXECUTION
// ========================================

async function initializeAdminSystem() {
  try {
    console.log('🚀 Starting admin system initialization...');
    console.log('📊 Project ID:', firebaseConfig.projectId);
    
    // Step 1: Create admin roles
    await createAdminRoles();
    
    // Step 2: Create admin permissions
    await createAdminPermissions();
    
    // Step 3: Create super admin user
    const superAdminUid = await createSuperAdminUser();
    
    // Step 4: Create system configuration
    await db.collection('system_config').doc('admin_system').set({
      initialized: true,
      initializedAt: admin.firestore.Timestamp.now(),
      initializedBy: superAdminUid,
      version: '1.0.0',
      features: {
        roleBasedAccess: true,
        auditLogging: true,
        sessionManagement: true,
        permissionInheritance: true,
        multiFactorAuth: false,
      },
      settings: {
        sessionTimeout: 60,
        maxFailedLogins: 5,
        lockoutDuration: 30,
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: true,
        }
      }
    });

    console.log('🎉 Admin system initialization completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('✅ Created 4 admin roles');
    console.log('✅ Created 25+ permissions');
    console.log('✅ Created super admin user');
    console.log('✅ Created system configuration');
    console.log('');
    console.log('🔐 Login Credentials:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: AdminPassword123!');
    console.log('');
    console.log('🔍 Check your Firebase Console:');
    console.log('- Firestore Database should now have admin collections');
    console.log('- Authentication should have the admin user');
    
  } catch (error) {
    console.error('❌ Error initializing admin system:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// Helper function
function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.replace(/-/g, ' ').slice(1);
}

// Run the initialization
initializeAdminSystem();
