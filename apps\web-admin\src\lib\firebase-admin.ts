import { initializeApp, getApps, cert, App } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore } from 'firebase-admin/firestore';
import { getStorage, Storage } from 'firebase-admin/storage';
import { getMessaging, Messaging } from 'firebase-admin/messaging';

// Server-side only - ensure this runs only on the server
if (typeof window !== 'undefined') {
  throw new Error('Firebase Admin SDK should only be used on the server side');
}

// Firebase Admin singleton
let adminApp: App | null = null;
let adminAuth: Auth | null = null;
let adminDb: Firestore | null = null;
let adminStorage: Storage | null = null;
let adminMessaging: Messaging | null = null;

// Initialize Firebase Admin
export function initializeFirebaseAdmin() {
  // Get environment variables (server-side only)
  const projectId = process.env.FIREBASE_ADMIN_PROJECT_ID;
  const privateKey = process.env.FIREBASE_ADMIN_PRIVATE_KEY;
  const clientEmail = process.env.FIREBASE_ADMIN_CLIENT_EMAIL;

  if (!projectId || !privateKey || !clientEmail) {
    throw new Error(
      'Firebase Admin credentials are missing. Please check your environment variables:\n' +
      '- FIREBASE_ADMIN_PROJECT_ID\n' +
      '- FIREBASE_ADMIN_PRIVATE_KEY\n' +
      '- FIREBASE_ADMIN_CLIENT_EMAIL'
    );
  }

  // Initialize app if not already initialized
  if (!adminApp && getApps().length === 0) {
    try {
      adminApp = initializeApp({
        credential: cert({
          projectId,
          privateKey: privateKey.replace(/\\n/g, '\n'), // Handle escaped newlines
          clientEmail,
        }),
        projectId,
      });
    } catch (error) {
      console.error('Failed to initialize Firebase Admin:', error);
      throw new Error('Failed to initialize Firebase Admin SDK');
    }
  } else if (getApps().length > 0) {
    adminApp = getApps()[0];
  }

  // Initialize services
  if (adminApp) {
    if (!adminAuth) {
      adminAuth = getAuth(adminApp);
    }
    if (!adminDb) {
      adminDb = getFirestore(adminApp);
    }
    if (!adminStorage) {
      adminStorage = getStorage(adminApp);
    }
    if (!adminMessaging) {
      adminMessaging = getMessaging(adminApp);
    }
  }

  return {
    app: adminApp,
    auth: adminAuth,
    db: adminDb,
    storage: adminStorage,
    messaging: adminMessaging,
  };
}

// Lazy initialization - only initialize when needed
export function getFirebaseAdmin() {
  if (!adminApp) {
    return initializeFirebaseAdmin();
  }
  
  return {
    app: adminApp,
    auth: adminAuth!,
    db: adminDb!,
    storage: adminStorage!,
    messaging: adminMessaging!,
  };
}

// Individual service getters
export function getAdminAuth(): Auth {
  const { auth } = getFirebaseAdmin();
  return auth;
}

export function getAdminDb(): Firestore {
  const { db } = getFirebaseAdmin();
  return db;
}

export function getAdminStorage(): Storage {
  const { storage } = getFirebaseAdmin();
  return storage;
}

export function getAdminMessaging(): Messaging {
  const { messaging } = getFirebaseAdmin();
  return messaging;
}

// Admin-specific helper functions
export async function verifyIdToken(idToken: string) {
  try {
    const auth = getAdminAuth();
    const decodedToken = await auth.verifyIdToken(idToken);
    return decodedToken;
  } catch (error) {
    console.error('Error verifying ID token:', error);
    throw new Error('Invalid ID token');
  }
}

export async function createCustomToken(uid: string, additionalClaims?: object) {
  try {
    const auth = getAdminAuth();
    const customToken = await auth.createCustomToken(uid, additionalClaims);
    return customToken;
  } catch (error) {
    console.error('Error creating custom token:', error);
    throw new Error('Failed to create custom token');
  }
}

export async function getUserByEmail(email: string) {
  try {
    const auth = getAdminAuth();
    const userRecord = await auth.getUserByEmail(email);
    return userRecord;
  } catch (error) {
    console.error('Error getting user by email:', error);
    throw new Error('User not found');
  }
}

export async function setCustomUserClaims(uid: string, customClaims: object) {
  try {
    const auth = getAdminAuth();
    await auth.setCustomUserClaims(uid, customClaims);
  } catch (error) {
    console.error('Error setting custom user claims:', error);
    throw new Error('Failed to set custom user claims');
  }
}

// Admin-specific user management functions
export async function createUser(userData: {
  email: string;
  password?: string;
  displayName?: string;
  disabled?: boolean;
  emailVerified?: boolean;
}) {
  try {
    const auth = getAdminAuth();
    const userRecord = await auth.createUser(userData);
    return userRecord;
  } catch (error) {
    console.error('Error creating user:', error);
    throw new Error('Failed to create user');
  }
}

export async function updateUser(uid: string, userData: {
  email?: string;
  displayName?: string;
  disabled?: boolean;
  emailVerified?: boolean;
}) {
  try {
    const auth = getAdminAuth();
    const userRecord = await auth.updateUser(uid, userData);
    return userRecord;
  } catch (error) {
    console.error('Error updating user:', error);
    throw new Error('Failed to update user');
  }
}

export async function deleteUser(uid: string) {
  try {
    const auth = getAdminAuth();
    await auth.deleteUser(uid);
  } catch (error) {
    console.error('Error deleting user:', error);
    throw new Error('Failed to delete user');
  }
}

export async function listUsers(maxResults = 1000) {
  try {
    const auth = getAdminAuth();
    const listUsersResult = await auth.listUsers(maxResults);
    return listUsersResult;
  } catch (error) {
    console.error('Error listing users:', error);
    throw new Error('Failed to list users');
  }
}

// Re-export types for convenience
export type {
  App as AdminApp,
  Auth as AdminAuth,
  Firestore as AdminFirestore,
  Storage as AdminStorage,
  Messaging as AdminMessaging,
};
