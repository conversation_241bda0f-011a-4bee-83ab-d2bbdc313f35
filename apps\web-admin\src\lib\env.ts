import { validateAdminEnv, type AdminEnv } from '@encreasl/env';

// Validate environment variables at startup
export const env: AdminEnv = validateAdminEnv();

// Export commonly used environment variables for easy access
export const {
  NEXT_PUBLIC_APP_NAME,
  NEXT_PUBLIC_APP_VERSION,
  NEXT_PUBLIC_APP_URL,
  NEXT_PUBLIC_FIREBASE_API_KEY,
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  NEXT_PUBLIC_REQUIRE_2FA,
  NEXT_PUBLIC_SESSION_TIMEOUT,
  NEXT_PUBLIC_MAX_LOGIN_ATTEMPTS,
  NEXT_PUBLIC_DEBUG_MODE,
  NEXT_PUBLIC_SHOW_DEV_TOOLS,
} = env;

// Helper functions for common environment checks
export const isProduction = env.NODE_ENV === 'production';
export const isDevelopment = env.NODE_ENV === 'development';
export const isTest = env.NODE_ENV === 'test';

// Firebase configuration object
export const firebaseConfig = {
  apiKey: env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: env.NEXT_PUBLIC_FIREBASE_APP_ID,
  ...(env.NEXT_PUBLIC_FIREBASE_VAPID_KEY && {
    vapidKey: env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
  }),
};

// Admin-specific feature flags
export const adminFeatures = {
  userManagement: env.NEXT_PUBLIC_ENABLE_USER_MANAGEMENT,
  analyticsDashboard: env.NEXT_PUBLIC_ENABLE_ANALYTICS_DASHBOARD,
  contentManagement: env.NEXT_PUBLIC_ENABLE_CONTENT_MANAGEMENT,
  campaignManagement: env.NEXT_PUBLIC_ENABLE_CAMPAIGN_MANAGEMENT,
  debug: env.NEXT_PUBLIC_DEBUG_MODE,
  devTools: env.NEXT_PUBLIC_SHOW_DEV_TOOLS,
};

// Security configuration
export const securityConfig = {
  requireTwoFA: env.NEXT_PUBLIC_REQUIRE_2FA,
  sessionTimeout: env.NEXT_PUBLIC_SESSION_TIMEOUT,
  maxLoginAttempts: env.NEXT_PUBLIC_MAX_LOGIN_ATTEMPTS,
  enableCSP: env.ADMIN_ENABLE_CSP,
  enableHSTS: env.ADMIN_ENABLE_HSTS,
};

// Admin API configuration
export const adminApiConfig = {
  baseUrl: env.ADMIN_API_BASE_URL,
  publicBaseUrl: env.NEXT_PUBLIC_ADMIN_API_BASE_URL,
};

// Database configuration (server-side only)
export const databaseConfig = {
  url: env.ADMIN_DATABASE_URL,
  poolSize: env.ADMIN_DATABASE_POOL_SIZE,
};

// Admin notifications
export const adminNotifications = {
  slackWebhook: env.ADMIN_SLACK_WEBHOOK_URL,
  discordWebhook: env.ADMIN_DISCORD_WEBHOOK_URL,
  notificationEmail: env.ADMIN_NOTIFICATION_EMAIL,
  alertEmail: env.ADMIN_ALERT_EMAIL,
};

// Logging configuration
export const loggingConfig = {
  level: env.ADMIN_LOG_LEVEL,
  enableAuditLogs: env.ADMIN_ENABLE_AUDIT_LOGS,
};

// Server-side only environment variables (use with caution)
export function getServerEnv() {
  if (typeof window !== 'undefined') {
    throw new Error('getServerEnv() can only be called on the server side');
  }
  
  return {
    firebaseAdmin: {
      projectId: env.FIREBASE_ADMIN_PROJECT_ID,
      privateKey: env.FIREBASE_ADMIN_PRIVATE_KEY,
      clientEmail: env.FIREBASE_ADMIN_CLIENT_EMAIL,
    },
    database: databaseConfig,
    notifications: adminNotifications,
  };
}
