import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  User,
  UserCredential,
  sendEmailVerification,
  GoogleAuthProvider,
  signInWithPopup,
  onAuthStateChanged,
  Unsubscribe,
  multiFactor,
  PhoneAuthProvider,
  PhoneMultiFactorGenerator,
} from 'firebase/auth';
import { firebaseAuth } from './firebase';
import { securityConfig } from './env';

// Authentication error types
export interface AuthError {
  code: string;
  message: string;
}

// Admin user profile with additional fields
export interface AdminUserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
  isAdmin: boolean;
  permissions: string[];
  lastLogin: Date | null;
  mfaEnabled: boolean;
}

// Sign in with email and password (admin-specific)
export async function signInAdmin(email: string, password: string): Promise<UserCredential> {
  try {
    const userCredential = await signInWithEmailAndPassword(firebaseAuth, email, password);
    
    // Check if 2FA is required and enabled
    if (securityConfig.requireTwoFA && userCredential.user) {
      const multiFactorUser = multiFactor(userCredential.user);
      if (multiFactorUser.enrolledFactors.length === 0) {
        throw new Error('Two-factor authentication is required for admin access');
      }
    }
    
    return userCredential;
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Create admin user (restricted)
export async function createAdminUser(email: string, password: string, displayName?: string): Promise<UserCredential> {
  try {
    const userCredential = await createUserWithEmailAndPassword(firebaseAuth, email, password);
    
    // Update profile with display name if provided
    if (displayName && userCredential.user) {
      await updateProfile(userCredential.user, { displayName });
    }
    
    // Send email verification
    if (userCredential.user) {
      await sendEmailVerification(userCredential.user);
    }
    
    return userCredential;
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Sign in with Google (admin)
export async function signInWithGoogleAdmin(): Promise<UserCredential> {
  try {
    const provider = new GoogleAuthProvider();
    provider.addScope('email');
    provider.addScope('profile');
    provider.setCustomParameters({
      hd: 'encreasl.com', // Restrict to company domain
    });
    
    return await signInWithPopup(firebaseAuth, provider);
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Sign out with session cleanup
export async function signOutAdmin(): Promise<void> {
  try {
    await signOut(firebaseAuth);
    // Clear any admin-specific session data
    if (typeof window !== 'undefined') {
      localStorage.removeItem('admin_session');
      sessionStorage.clear();
    }
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Reset password
export async function resetPassword(email: string): Promise<void> {
  try {
    await sendPasswordResetEmail(firebaseAuth, email);
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Update user profile
export async function updateUserProfile(user: User, profile: { displayName?: string; photoURL?: string }): Promise<void> {
  try {
    await updateProfile(user, profile);
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Send email verification
export async function sendVerificationEmail(user: User): Promise<void> {
  try {
    await sendEmailVerification(user);
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Get current user
export function getCurrentUser(): User | null {
  return firebaseAuth.currentUser;
}

// Get admin user profile data
export function getAdminUserProfile(user: User): AdminUserProfile {
  return {
    uid: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    emailVerified: user.emailVerified,
    isAdmin: true, // This would be determined by custom claims in production
    permissions: ['read', 'write', 'admin'], // This would come from custom claims
    lastLogin: new Date(),
    mfaEnabled: multiFactor(user).enrolledFactors.length > 0,
  };
}

// Auth state listener
export function onAuthStateChange(callback: (user: User | null) => void): Unsubscribe {
  return onAuthStateChanged(firebaseAuth, callback);
}

// Get ID token for API calls
export async function getIdToken(forceRefresh = false): Promise<string | null> {
  const user = getCurrentUser();
  if (!user) return null;
  
  try {
    return await user.getIdToken(forceRefresh);
  } catch (error) {
    console.error('Error getting ID token:', error);
    return null;
  }
}

// Check if user is authenticated admin
export function isAuthenticatedAdmin(): boolean {
  const user = getCurrentUser();
  if (!user) return false;
  
  // In production, check custom claims for admin role
  return user.emailVerified;
}

// Check if user email is verified
export function isEmailVerified(): boolean {
  const user = getCurrentUser();
  return user ? user.emailVerified : false;
}

// Check if MFA is enabled
export function isMfaEnabled(): boolean {
  const user = getCurrentUser();
  if (!user) return false;
  
  return multiFactor(user).enrolledFactors.length > 0;
}

// Session timeout check
export function checkSessionTimeout(): boolean {
  if (typeof window === 'undefined') return false;
  
  const lastActivity = localStorage.getItem('last_activity');
  if (!lastActivity) return true;
  
  const now = Date.now();
  const sessionTimeout = securityConfig.sessionTimeout * 1000; // Convert to milliseconds
  
  return (now - parseInt(lastActivity)) > sessionTimeout;
}

// Update last activity
export function updateLastActivity(): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('last_activity', Date.now().toString());
  }
}

// Helper function to get user-friendly error messages
function getAuthErrorMessage(errorCode: string): string {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No admin account found with this email address.';
    case 'auth/wrong-password':
      return 'Incorrect password.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists.';
    case 'auth/weak-password':
      return 'Password should be at least 8 characters with mixed case, numbers, and symbols.';
    case 'auth/invalid-email':
      return 'Invalid email address.';
    case 'auth/user-disabled':
      return 'This admin account has been disabled.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Account temporarily locked.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection.';
    case 'auth/popup-closed-by-user':
      return 'Sign-in popup was closed before completion.';
    case 'auth/cancelled-popup-request':
      return 'Sign-in was cancelled.';
    case 'auth/admin-restricted-operation':
      return 'This operation is restricted to super administrators.';
    default:
      return 'An error occurred during authentication.';
  }
}

// Export types
export type { User, UserCredential, Unsubscribe };
