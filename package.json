{"name": "encreasl", "version": "0.1.0", "private": true, "scripts": {"build": "turbo run build", "build:web": "turbo run build --filter=@encreasl/web", "build:admin": "turbo run build --filter=@encreasl/web-admin", "dev": "turbo run dev", "dev:web": "turbo run dev --filter=@encreasl/web", "dev:admin": "turbo run dev --filter=@encreasl/web-admin", "lint": "turbo run lint", "type-check": "turbo run type-check", "setup": "node scripts/setup-env.js", "clean": "turbo run clean"}, "devDependencies": {"turbo": "^2.5.5"}, "packageManager": "pnpm@10.12.1"}