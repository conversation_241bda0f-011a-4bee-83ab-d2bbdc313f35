import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  transpilePackages: ["@encreasl/ui"],

  // Environment variables configuration for admin app
  env: {
    // Custom environment variables that should be available at build time
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // Public runtime configuration (deprecated in favor of NEXT_PUBLIC_ prefix)
  publicRuntimeConfig: {
    // These will be available on both server and client
    appName: process.env.NEXT_PUBLIC_APP_NAME,
    appVersion: process.env.NEXT_PUBLIC_APP_VERSION,
    requireTwoFA: process.env.NEXT_PUBLIC_REQUIRE_2FA,
  },

  // Server runtime configuration (admin-specific)
  serverRuntimeConfig: {
    // These will only be available on the server side
    firebaseAdminPrivateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY,
    firebaseAdminClientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
    adminDatabaseUrl: process.env.ADMIN_DATABASE_URL,
    adminSlackWebhook: process.env.ADMIN_SLACK_WEBHOOK_URL,
  },

  turbopack: {
    resolveAlias: {
      "@/*": "./src/*",
    },
  },

  // Admin-specific security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },

  // Experimental features for better environment variable handling
  experimental: {
    // Enable environment variable validation
    typedRoutes: true,
  },
};

export default nextConfig;
